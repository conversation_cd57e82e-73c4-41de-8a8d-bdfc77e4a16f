import React from 'react';
import { MainLayout } from '@/components/layout/main-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { MessageSquare, ThumbsUp, Eye, Clock, TrendingUp, Users } from 'lucide-react';
import { formatRelativeTime } from '@/lib/utils';

// Mock data for demonstration
const mockUser = {
  id: '1',
  name: '<PERSON>',
  email: '<EMAIL>',
  avatar: '',
  role: 'user' as const,
};

const mockQuestions = [
  {
    id: '1',
    title: 'How to implement authentication in Next.js 15?',
    content: 'I\'m trying to set up authentication in my Next.js 15 application using the new app router. What are the best practices?',
    author: { name: '<PERSON>', avatar: '' },
    tags: ['nextjs', 'authentication', 'react'],
    votes: 15,
    answers: 3,
    views: 127,
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    isAnswered: true,
  },
  {
    id: '2',
    title: 'TypeScript generic constraints not working as expected',
    content: 'I have a generic function with constraints but TypeScript is not inferring the types correctly...',
    author: { name: 'Bob Smith', avatar: '' },
    tags: ['typescript', 'generics', 'types'],
    votes: 8,
    answers: 1,
    views: 89,
    createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
    isAnswered: false,
  },
  {
    id: '3',
    title: 'Best practices for React state management in 2024',
    content: 'With so many state management solutions available, what would you recommend for a medium-sized React application?',
    author: { name: 'Carol Davis', avatar: '' },
    tags: ['react', 'state-management', 'redux', 'zustand'],
    votes: 23,
    answers: 7,
    views: 234,
    createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
    isAnswered: true,
  },
];

const stats = [
  { label: 'Questions', value: '1,234', icon: MessageSquare, color: 'text-blue-600' },
  { label: 'Users', value: '567', icon: Users, color: 'text-green-600' },
  { label: 'Answers', value: '2,891', icon: ThumbsUp, color: 'text-purple-600' },
  { label: 'Views', value: '45.2K', icon: Eye, color: 'text-orange-600' },
];

export default function Home() {
  return (
    <MainLayout user={mockUser}>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="text-center py-8">
          <h1 className="text-4xl font-bold mb-4">Welcome to StackIt</h1>
          <p className="text-xl text-muted-foreground mb-6 max-w-2xl mx-auto">
            A modern Q&A platform where developers help each other solve problems and share knowledge.
          </p>
          <div className="flex gap-4 justify-center">
            <Button size="lg">
              Ask a Question
            </Button>
            <Button variant="outline" size="lg">
              Browse Questions
            </Button>
          </div>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {stats.map((stat) => (
            <Card key={stat.label}>
              <CardContent className="p-6">
                <div className="flex items-center gap-3">
                  <stat.icon className={`h-8 w-8 ${stat.color}`} />
                  <div>
                    <p className="text-2xl font-bold">{stat.value}</p>
                    <p className="text-sm text-muted-foreground">{stat.label}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Recent Questions */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold">Recent Questions</h2>
            <Button variant="outline">View All</Button>
          </div>

          <div className="space-y-4">
            {mockQuestions.map((question) => (
              <Card key={question.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex gap-4">
                    {/* Vote and Stats */}
                    <div className="flex flex-col items-center gap-2 text-sm text-muted-foreground min-w-[80px]">
                      <div className="flex items-center gap-1">
                        <ThumbsUp className="h-4 w-4" />
                        <span>{question.votes}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <MessageSquare className="h-4 w-4" />
                        <span>{question.answers}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Eye className="h-4 w-4" />
                        <span>{question.views}</span>
                      </div>
                    </div>

                    {/* Question Content */}
                    <div className="flex-1">
                      <div className="flex items-start justify-between gap-4">
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold mb-2 hover:text-primary cursor-pointer">
                            {question.title}
                          </h3>
                          <p className="text-muted-foreground mb-3 line-clamp-2">
                            {question.content}
                          </p>

                          {/* Tags */}
                          <div className="flex flex-wrap gap-2 mb-3">
                            {question.tags.map((tag) => (
                              <Badge key={tag} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        {/* Answer Status */}
                        {question.isAnswered && (
                          <Badge variant="success" className="shrink-0">
                            Answered
                          </Badge>
                        )}
                      </div>

                      {/* Author and Time */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Avatar className="h-6 w-6">
                            <AvatarImage src={question.author.avatar} />
                            <AvatarFallback className="text-xs">
                              {question.author.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-sm text-muted-foreground">
                            {question.author.name}
                          </span>
                        </div>
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <Clock className="h-3 w-3" />
                          <span>{formatRelativeTime(question.createdAt)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
